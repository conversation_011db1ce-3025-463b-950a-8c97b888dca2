# IndexTTS API 接口功能说明

## 项目概述

IndexTTS 是一款工业级可控且高效的零样本文本转语音系统（Zero-Shot Text-To-Speech System）。该项目提供了基于 FastAPI 的 REST API 接口和 Gradio 的 Web UI 界面，支持高质量的语音合成功能。

## 系统架构

- **核心引擎**: IndexTTS 推理引擎
- **Web 框架**: FastAPI + Gradio
- **模型组件**: GPT + BigVGAN + VQ-VAE
- **支持格式**: WAV 音频输出

## 启动参数

```bash
python app.py [OPTIONS]
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--verbose` | bool | False | 启用详细日志模式 |
| `--port` | int | 8000 | Web 服务端口 |
| `--host` | str | 127.0.0.1 | Web 服务主机地址 |
| `--model_dir` | str | checkpoints | 模型检查点目录 |
| `--output_dir` | str | outputs | 音频输出目录 |

### 启动示例

```bash
# 默认启动
python app.py

# 自定义端口和主机
python app.py --host 0.0.0.0 --port 8080

# 启用详细日志
python app.py --verbose
```

## API 接口

### 1. 健康检查接口

**接口地址**: `GET /health`

**功能**: 检查服务运行状态

**响应格式**:
```json
{
    "code": 0,
    "status": "ok", 
    "message": "服务运行正常"
}
```

**示例**:
```bash
curl -X GET "http://localhost:8000/health"
```

### 2. 语音生成接口

**接口地址**: `POST /generate`

**功能**: 根据参考音频和文本生成语音

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `text` | string | ✓ | - | 要转换的文本内容 |
| `base64_data` | boolean | ✗ | false | 是否返回 base64 编码的音频数据 |
| `prompt_audio` | string | ✗ | "./prompts/sample.wav" | 参考音频文件路径 |
| `infer_mode` | string | ✗ | "普通推理" | 推理模式："普通推理" 或 "批次推理" |
| `max_text_tokens_per_sentence` | integer | ✗ | 120 | 分句最大 Token 数 |
| `sentences_bucket_max_size` | integer | ✗ | 4 | 分句分桶最大容量（批次推理生效） |

#### 高级参数

| 参数名 | 类型 | 默认值 | 范围 | 说明 |
|--------|------|--------|------|------|
| `do_sample` | boolean | true | - | 是否进行采样 |
| `top_p` | float | 0.8 | 0.0-1.0 | Top-p 采样参数 |
| `top_k` | integer | 30 | 0-100 | Top-k 采样参数 |
| `temperature` | float | 1.0 | 0.1-2.0 | 温度参数，控制随机性 |
| `length_penalty` | float | 0.0 | -2.0-2.0 | 长度惩罚 |
| `num_beams` | integer | 3 | 1-10 | Beam search 数量 |
| `repetition_penalty` | float | 10.0 | 0.1-20.0 | 重复惩罚 |
| `max_mel_tokens` | integer | 600 | 50-max | 生成 Token 最大数量 |

#### 响应格式

**成功响应**:
```json
{
    "code": 0,
    "desc": "success",
    "data": {
        "stream_url": "http://localhost:8000/outputs/spk_1234567890.wav",
        "audio_base64": "UklGRiQAAABXQVZFZm10..." // 仅当 base64_data=true 时返回
    }
}
```

**错误响应**:
```json
{
    "code": 1,
    "desc": "生成失败: 错误信息",
    "data": null
}
```

#### 请求示例

**基础请求**:
```bash
curl -X POST "http://localhost:8000/generate" \
     -H "Content-Type: application/json" \
     -d '{
         "text": "你好，这是一个测试语音。",
         "prompt_audio": "./prompts/sample.wav"
     }'
```

**完整参数请求**:
```bash
curl -X POST "http://localhost:8000/generate" \
     -H "Content-Type: application/json" \
     -d '{
         "text": "大家好，我现在正在体验AI科技，说实话，来之前我绝对想不到！AI技术已经发展到这样匪夷所思的地步了！",
         "base64_data": true,
         "prompt_audio": "./prompts/sample.wav",
         "infer_mode": "批次推理",
         "max_text_tokens_per_sentence": 100,
         "sentences_bucket_max_size": 6,
         "do_sample": true,
         "top_p": 0.9,
         "top_k": 50,
         "temperature": 1.2,
         "length_penalty": 0.1,
         "num_beams": 5,
         "repetition_penalty": 8.0,
         "max_mel_tokens": 800
     }'
```

**Python 请求示例**:
```python
import requests
import json

url = "http://localhost:8000/generate"
data = {
    "text": "欢迎使用IndexTTS语音合成系统！",
    "prompt_audio": "./prompts/sample.wav",
    "infer_mode": "批次推理",
    "base64_data": False
}

response = requests.post(url, json=data)
result = response.json()

if result["code"] == 0:
    print(f"生成成功，音频URL: {result['data']['stream_url']}")
else:
    print(f"生成失败: {result['desc']}")
```

### 3. 文件下载接口

**接口地址**: `GET /download/{output_path}`

**功能**: 直接下载生成的音频文件

**参数**:
- `output_path`: 音频文件名

**示例**:
```bash
curl -X GET "http://localhost:8000/download/spk_1234567890.wav" \
     --output "downloaded_audio.wav"
```

### 4. 静态文件访问

**接口地址**: `GET /outputs/{filename}`

**功能**: 通过静态文件服务访问生成的音频

**示例**:
```bash
# 浏览器直接访问
http://localhost:8000/outputs/spk_1234567890.wav
```

## 推理模式说明

### 普通推理
- **特点**: 逐句处理，质量更高
- **适用**: 短文本、对质量要求高的场景
- **性能**: 相对较慢，内存占用较少

### 批次推理
- **特点**: 批量处理，速度更快
- **适用**: 长文本、对速度要求高的场景
- **性能**: 速度提升2-10倍，内存占用较多

## 参数调优建议

### 文本分句参数
- `max_text_tokens_per_sentence`: 建议 80-200
  - 值越大：分句越长，质量可能更好，速度较慢
  - 值越小：分句越碎，速度较快，质量可能下降

### 批次处理参数
- `sentences_bucket_max_size`: 建议 2-8
  - 值越大：批次越大，速度越快，内存占用越多
  - 值越小：批次越小，速度较慢，内存占用较少

### 采样参数
- `temperature`: 控制随机性
  - 较低值(0.5-0.8)：更稳定，重复性高
  - 较高值(1.0-1.5)：更多样，创造性强
- `top_p`: 核采样参数
  - 0.8-0.95：平衡质量和多样性
- `repetition_penalty`: 重复惩罚
  - 5.0-15.0：避免重复，值过高可能影响自然度

## Web UI 界面

除了 API 接口，系统还提供了 Gradio Web UI 界面：

**访问地址**: `http://localhost:8000`

**功能特性**:
- 音频上传和录制
- 实时文本分句预览
- 高级参数调节
- 音频播放和下载
- 示例案例展示

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 0 | 成功 | - |
| 1 | 生成失败 | 检查参数和模型文件 |

### 常见问题

1. **模型文件缺失**
   - 确保 `checkpoints` 目录包含所需模型文件
   - 检查 `bigvgan_generator.pth`, `gpt.pth`, `bpe.model`, `config.yaml`

2. **内存不足**
   - 减少 `sentences_bucket_max_size`
   - 降低 `max_text_tokens_per_sentence`
   - 使用普通推理模式

3. **音频质量问题**
   - 检查参考音频质量
   - 调整采样参数
   - 增加 `max_text_tokens_per_sentence`

## 性能优化

### 硬件要求
- **GPU**: 推荐 NVIDIA GPU (CUDA 支持)
- **内存**: 建议 8GB+ RAM
- **存储**: 足够空间存储模型和输出文件

### 优化建议
1. 使用 GPU 加速
2. 启用 FP16 精度
3. 合理设置批次大小
4. 缓存参考音频特征

## 许可证

请参考项目根目录的 LICENSE 文件了解许可证信息。
